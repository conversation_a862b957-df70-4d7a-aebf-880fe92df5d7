"""
修复用户密码哈希的管理命令

这个命令用于检查和修复数据库中可能存在的明文密码问题。
当发现用户密码没有正确哈希时，会使用原密码重新设置哈希。

使用方法：
    uv run python manage.py fix_password_hashes
    uv run python manage.py fix_password_hashes --dry-run  # 只检查不修复
    uv run python manage.py fix_password_hashes --verbose  # 显示详细信息
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from apps.users.models import UserAccount


class Command(BaseCommand):
    help = '检查和修复用户密码哈希问题'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='只检查问题，不进行修复',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='显示详细信息',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        verbose = options['verbose']
        
        self.stdout.write(
            self.style.SUCCESS('开始检查用户密码哈希...')
        )
        
        # 获取所有用户
        users = UserAccount.objects.all()
        total_users = users.count()
        problematic_users = []
        
        self.stdout.write(f'总共找到 {total_users} 个用户')
        
        # 检查每个用户的密码
        for user in users:
            is_hashed = user.password.startswith('pbkdf2_sha256$')
            
            if verbose:
                status = "✅ 已哈希" if is_hashed else "❌ 明文"
                self.stdout.write(f'  {user.email}: {status}')
            
            if not is_hashed:
                problematic_users.append(user)
                if verbose:
                    self.stdout.write(
                        self.style.WARNING(f'    明文密码: {user.password}')
                    )
        
        if not problematic_users:
            self.stdout.write(
                self.style.SUCCESS('✅ 所有用户密码都已正确哈希化')
            )
            return
        
        # 报告问题
        self.stdout.write(
            self.style.WARNING(
                f'⚠️  发现 {len(problematic_users)} 个用户存在明文密码问题:'
            )
        )
        
        for user in problematic_users:
            self.stdout.write(f'  - {user.email}')
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('--dry-run 模式，不进行修复')
            )
            return
        
        # 修复密码哈希
        self.stdout.write('\n开始修复密码哈希...')
        
        fixed_count = 0
        with transaction.atomic():
            for user in problematic_users:
                try:
                    original_password = user.password
                    user.set_password(original_password)
                    user.save()
                    fixed_count += 1
                    
                    if verbose:
                        self.stdout.write(
                            self.style.SUCCESS(f'  ✅ 已修复: {user.email}')
                        )
                    
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'  ❌ 修复失败 {user.email}: {e}')
                    )
        
        # 总结
        self.stdout.write(
            self.style.SUCCESS(
                f'\n修复完成！成功修复了 {fixed_count}/{len(problematic_users)} 个用户的密码哈希'
            )
        )
        
        if fixed_count < len(problematic_users):
            self.stdout.write(
                self.style.WARNING(
                    f'有 {len(problematic_users) - fixed_count} 个用户修复失败，请检查日志'
                )
            )
