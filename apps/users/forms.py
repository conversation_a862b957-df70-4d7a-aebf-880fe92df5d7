from django import forms
from django.contrib.auth.forms import ReadOnlyPasswordHashField

from .models import UserAccount


class UserAccountCreationForm(forms.ModelForm):
    """后台创建用户表单，使用email + password1/password2，并正确hash密码"""
    password1 = forms.CharField(label="密码", widget=forms.PasswordInput)
    password2 = forms.CharField(label="确认密码", widget=forms.PasswordInput)

    class Meta:
        model = UserAccount
        fields = ("email",)

    def clean_password2(self):
        password1 = self.cleaned_data.get("password1")
        password2 = self.cleaned_data.get("password2")
        if password1 and password2 and password1 != password2:
            raise forms.ValidationError("两次输入的密码不一致")
        return password2

    def save(self, commit=True):
        user = super().save(commit=False)
        user.set_password(self.cleaned_data["password1"])
        if commit:
            user.save()
        return user


class UserAccountChangeForm(forms.ModelForm):
    """后台编辑用户表单，密码以只读哈希显示"""
    password = ReadOnlyPasswordHashField(label="密码",
                                         help_text=(
                                             "原始密码不存储。要修改密码，请使用“更改密码”表单。"
                                         ))

    class Meta:
        model = UserAccount
        fields = ("email", "password", "is_active", "is_staff", "is_superuser", "groups", "user_permissions")

    def clean_password(self):
        # 返回初始值，不修改密码
        return self.initial.get("password")

